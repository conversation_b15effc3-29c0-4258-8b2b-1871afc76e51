<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Repositories\User\UserRepositoryInterface;
use App\Models\User;
use App\Models\Permission;

class UserController extends Controller
{
    protected UserRepositoryInterface $userRepository;

    public function __construct(UserRepositoryInterface $userRepository)
    {
        $this->userRepository = $userRepository;
    }

    public function getAllUsers(Request $request)
    {
        $dataSearch = $request->all();
        $result = $this->userRepository->getAllUsers($dataSearch);
        if (isset($result)) {

            return response()->json([
                'status' => 'success',
                'counts' => $result['counts'],
                'users' => $result['users'],
            ], 200);
        } else {

            return response()->json(['status' => 'not_data'], 200);
        }
    }

    private function getUserPermissions(User $user)
    {
        // L<PERSON>y danh sách quyền từ vai trò của người dùng
        $permissions = $user->getAllPermissions();
        if ($permissions->isEmpty()) {
            return [];
        }
        
        // Lấy danh sách slug các quyền
        return $permissions->pluck('slug')->toArray();
    }

    public function updateUserRoles(Request $request, $id)
    {
        
        try {
            $request->validate([
                'role_ids' => 'required|array',
                'role_ids.*' => 'exists:roles,id'
            ]);
            $user = $this->userRepository->find($id);

            if (!$user) {
                return response()->json([
                    'status' => 'error',
                    'message' => __('error.404'),
                ], 404);
            }

            // Update user roles
            $user->syncRoles($request->role_ids);

            $user = $request->user();
        $permissions = $user->hasRole(config('constants.auth.super_admin')) ? Permission::all()->pluck('slug')->toArray() : $this->getUserPermissions($user);
        $tokenResult = $user->createToken('Personal Access Token', $permissions);
        $token = $tokenResult->token;
        $token->save();

            return response()->json([
                'status' => 'success',
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            
            return response()->json([
                'status' => 'error',
                'message' =>  __('error.422'),
                'errors' => $e->errors(),
            ], 422);
        } catch (\Exception $th) {

            return response()->json([
                'status' => 'error',
                'message' => __('error.500'),
                'error_details' => $th->getMessage(),
            ], 500);
        }
    }
}
